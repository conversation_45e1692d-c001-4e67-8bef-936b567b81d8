// Minimal functions index - admin functions only
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

console.log('🚀 Minimal Firebase Functions with Admin Support loading...');

// Import admin functions
export * from './admin-only';

// Test function
export const testEssential = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Essential functions working',
      timestamp: new Date().toISOString()
    });
  });

// Additional admin functions for transaction and listing management
export const adminManageTransaction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify admin authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }
      
      const { transactionId, action, reason } = data;
      
      if (!transactionId || !action) {
        throw new functions.https.HttpsError('invalid-argument', 'Transaction ID and action are required');
      }

      const orderRef = admin.firestore().collection('orders').doc(transactionId);
      const orderDoc = await orderRef.get();
      
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Transaction not found');
      }

      const orderData = orderDoc.data();
      let updateData: any = {
        updatedAt: admin.firestore.Timestamp.now()
      };

      switch (action) {
        case 'refund':
          updateData.status = 'refunded';
          updateData.refundAmount = orderData?.totalAmount || 0;
          updateData.refundReason = reason || 'Admin refund';
          updateData.refundedAt = admin.firestore.Timestamp.now();
          updateData.refundedBy = context.auth.uid;
          
          // Update listing back to active if needed
          if (orderData?.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            
            if (listingDoc.exists && listingDoc.data()?.status === 'sold') {
              await listingRef.update({
                status: 'active',
                updatedAt: admin.firestore.Timestamp.now()
              });
            }
          }
          break;
          
        case 'release':
          updateData.status = 'completed';
          updateData.fundsReleasedAt = admin.firestore.Timestamp.now();
          updateData.fundsReleasedBy = context.auth.uid;
          updateData.releaseReason = reason || 'Admin release';
          break;
          
        default:
          throw new functions.https.HttpsError('invalid-argument', `Invalid action: ${action}`);
      }

      await orderRef.update(updateData);

      // Log admin action
      await admin.firestore().collection('adminLogs').add({
        adminId: context.auth.uid,
        action: `transaction_${action}`,
        targetTransactionId: transactionId,
        timestamp: admin.firestore.Timestamp.now(),
        metadata: { reason }
      });

      return {
        success: true,
        message: `Transaction ${action} completed successfully`,
        transactionId
      };

    } catch (error) {
      console.error('Error in adminManageTransaction:', error);
      throw new functions.https.HttpsError(
        'internal',
        `Failed to ${data.action} transaction`,
        error
      );
    }
  });

// Admin listing management function
export const adminManageListing = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify admin authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }
      
      const { listingId, action, reason } = data;
      
      if (!listingId || !action) {
        throw new functions.https.HttpsError('invalid-argument', 'Listing ID and action are required');
      }

      const listingRef = admin.firestore().collection('listings').doc(listingId);
      const listingDoc = await listingRef.get();
      
      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      let updateData: any = {
        updatedAt: admin.firestore.Timestamp.now(),
        moderatedAt: admin.firestore.Timestamp.now(),
        moderatedBy: context.auth.uid
      };

      switch (action) {
        case 'block':
          updateData.status = 'blocked';
          updateData.blockReason = reason || 'Blocked by admin';
          updateData.blockedAt = admin.firestore.Timestamp.now();
          updateData.blockedBy = context.auth.uid;
          break;
          
        case 'unblock':
          updateData.status = 'active';
          updateData.blockReason = admin.firestore.FieldValue.delete();
          updateData.blockedAt = admin.firestore.FieldValue.delete();
          updateData.blockedBy = admin.firestore.FieldValue.delete();
          break;
          
        case 'ban':
          updateData.status = 'banned';
          updateData.banReason = reason || 'Banned by admin';
          updateData.bannedAt = admin.firestore.Timestamp.now();
          updateData.bannedBy = context.auth.uid;
          break;
          
        case 'unban':
          updateData.status = 'active';
          updateData.banReason = admin.firestore.FieldValue.delete();
          updateData.bannedAt = admin.firestore.FieldValue.delete();
          updateData.bannedBy = admin.firestore.FieldValue.delete();
          break;
          
        case 'delete':
          updateData.status = 'deleted';
          updateData.deletedAt = admin.firestore.Timestamp.now();
          updateData.deletedBy = context.auth.uid;
          updateData.deleteReason = reason || 'Deleted by admin';
          break;
          
        case 'approve':
          updateData.status = 'active';
          break;
          
        case 'flag':
          updateData.status = 'flagged';
          updateData.flagReason = reason || 'Flagged by admin';
          updateData.flaggedAt = admin.firestore.Timestamp.now();
          updateData.flaggedBy = context.auth.uid;
          break;
          
        case 'unflag':
          updateData.status = 'active';
          updateData.flagReason = admin.firestore.FieldValue.delete();
          updateData.flaggedAt = admin.firestore.FieldValue.delete();
          updateData.flaggedBy = admin.firestore.FieldValue.delete();
          break;
          
        default:
          throw new functions.https.HttpsError('invalid-argument', `Invalid action: ${action}`);
      }

      await listingRef.update(updateData);

      // Log admin action
      await admin.firestore().collection('adminLogs').add({
        adminId: context.auth.uid,
        action: `listing_${action}`,
        targetListingId: listingId,
        timestamp: admin.firestore.Timestamp.now(),
        metadata: { reason }
      });

      return {
        success: true,
        message: `Listing ${action} completed successfully`,
        listingId
      };

    } catch (error) {
      console.error('Error in adminManageListing:', error);
      throw new functions.https.HttpsError(
        'internal',
        `Failed to ${data.action} listing`,
        error
      );
    }
  });

// Admin dashboard stats function
export const adminFetchDashboardStats = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 120,
  })
  .https.onCall(async (_data, context) => {
    try {
      // Verify admin authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      // Get all collections data in parallel
      const [usersSnapshot, listingsSnapshot, ordersSnapshot] = await Promise.all([
        admin.firestore().collection('users').get(),
        admin.firestore().collection('listings').get(),
        admin.firestore().collection('orders').get()
      ]);

      // Calculate user stats
      const totalUsers = usersSnapshot.size;
      const activeUsers = usersSnapshot.docs.filter(doc => {
        const data = doc.data();
        return data.status === 'active' || !data.status;
      }).length;

      // Calculate listing stats
      const totalListings = listingsSnapshot.size;
      const activeListings = listingsSnapshot.docs.filter(doc => {
        const data = doc.data();
        return data.status === 'active';
      }).length;

      // Calculate transaction stats
      const totalTransactions = ordersSnapshot.size;
      let totalRevenue = 0;
      let pendingTransactions = 0;

      ordersSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.totalAmount) {
          totalRevenue += data.totalAmount;
        }
        if (data.status === 'pending' || data.status === 'paid') {
          pendingTransactions++;
        }
      });

      // Get recent activity
      const recentActivity = await admin.firestore()
        .collection('adminLogs')
        .orderBy('timestamp', 'desc')
        .limit(10)
        .get();

      const activity = recentActivity.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        totalUsers,
        activeUsers,
        totalListings,
        activeListings,
        totalTransactions,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        pendingTransactions,
        recentActivity: activity
      };

    } catch (error) {
      console.error('Error in adminFetchDashboardStats:', error);
      throw new functions.https.HttpsError('internal', 'Failed to fetch dashboard stats', error);
    }
  });
