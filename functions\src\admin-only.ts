// Essential admin functions only
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

console.log('🚀 Essential Admin Functions loading...');

// Helper functions
const verifyAuth = async (context: functions.https.CallableContext): Promise<admin.auth.DecodedIdToken> => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }
  return context.auth as unknown as admin.auth.DecodedIdToken;
};

const verifyAdminAuth = async (context: functions.https.CallableContext): Promise<admin.auth.DecodedIdToken> => {
  const auth = await verifyAuth(context);
  
  // Check admin role in Firestore
  const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
  if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
    throw new functions.https.HttpsError(
      'permission-denied',
      'Admin access required'
    );
  }
  
  return auth;
};

// Get admin wallet data for any user
export const getAdminWalletData = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      await verifyAdminAuth(context);
      
      const { userId } = data;
      if (!userId) {
        throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
      }

      // Get wallet data
      const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
      
      if (!walletDoc.exists) {
        // Create default wallet if it doesn't exist
        const referralCode = `user${userId.substring(0, 6)}`;
        const defaultWallet = {
          userId,
          balance: 0,
          referralCode,
          usedReferral: false,
          history: [],
          grantedBy: 'system',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        };
        
        await admin.firestore().collection('wallets').doc(userId).set(defaultWallet);
        return defaultWallet;
      }

      return walletDoc.data();
    } catch (error) {
      console.error('Error in getAdminWalletData:', error);
      throw error;
    }
  });

// Grant wallet credits to users
export const grantWalletCredits = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      const adminAuth = await verifyAdminAuth(context);
      
      const { userId, amount, description } = data;
      
      if (!userId || !amount || amount <= 0) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid user ID and positive amount are required');
      }

      // Create transaction record
      const transaction = {
        id: admin.firestore().collection('temp').doc().id,
        userId: userId,
        type: 'admin_grant',
        amount: parseFloat(amount.toFixed(2)),
        description: description || `Admin credit grant`,
        timestamp: admin.firestore.Timestamp.now(),
        grantedBy: adminAuth.uid,
        source: 'admin_grant',
        createdAt: admin.firestore.Timestamp.now()
      };

      // Get or create wallet
      const walletRef = admin.firestore().collection('wallets').doc(userId);
      const walletDoc = await walletRef.get();

      if (!walletDoc.exists) {
        // Create new wallet
        const referralCode = `user${userId.substring(0, 6)}`;
        await walletRef.set({
          userId,
          balance: transaction.amount,
          referralCode,
          usedReferral: false,
          history: [transaction],
          grantedBy: 'admin',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        });
      } else {
        // Update existing wallet
        await walletRef.update({
          balance: admin.firestore.FieldValue.increment(transaction.amount),
          history: admin.firestore.FieldValue.arrayUnion(transaction),
          lastUpdated: admin.firestore.Timestamp.now()
        });
      }

      // Log admin action
      await admin.firestore().collection('adminLogs').add({
        adminId: adminAuth.uid,
        action: 'grant_wallet_credits',
        targetUserId: userId,
        amount: transaction.amount,
        timestamp: admin.firestore.Timestamp.now(),
        metadata: { description }
      });

      return {
        success: true,
        message: `Successfully granted $${transaction.amount} to user`,
        transaction
      };
    } catch (error) {
      console.error('Error in grantWalletCredits:', error);
      throw error;
    }
  });

// Admin user management function
export const adminManageUser = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      const adminAuth = await verifyAdminAuth(context);
      
      const { userId, action, data: actionData } = data;
      
      if (!userId || !action) {
        throw new functions.https.HttpsError('invalid-argument', 'User ID and action are required');
      }

      let updateData: any = {
        updatedAt: admin.firestore.Timestamp.now()
      };

      switch (action) {
        case 'ban':
          updateData.status = 'banned';
          updateData.bannedAt = admin.firestore.Timestamp.now();
          updateData.bannedBy = adminAuth.uid;
          updateData.banReason = actionData?.reason || 'Banned by admin';
          // Disable Firebase Auth account
          await admin.auth().updateUser(userId, { disabled: true });
          break;
          
        case 'unban':
          updateData.status = 'active';
          updateData.bannedAt = admin.firestore.FieldValue.delete();
          updateData.bannedBy = admin.firestore.FieldValue.delete();
          updateData.banReason = admin.firestore.FieldValue.delete();
          updateData.unbannedAt = admin.firestore.Timestamp.now();
          updateData.unbannedBy = adminAuth.uid;
          // Re-enable Firebase Auth account
          await admin.auth().updateUser(userId, { disabled: false });
          break;
          
        case 'block':
          updateData.status = 'blocked';
          updateData.blockedAt = admin.firestore.Timestamp.now();
          updateData.blockedBy = adminAuth.uid;
          updateData.blockReason = actionData?.reason || 'Blocked by admin';
          break;
          
        case 'unblock':
          updateData.status = 'active';
          updateData.blockedAt = admin.firestore.FieldValue.delete();
          updateData.blockedBy = admin.firestore.FieldValue.delete();
          updateData.blockReason = admin.firestore.FieldValue.delete();
          updateData.unblockedAt = admin.firestore.Timestamp.now();
          updateData.unblockedBy = adminAuth.uid;
          break;
          
        case 'warn':
          // Add warning to user's warnings array
          const warning = {
            id: admin.firestore().collection('temp').doc().id,
            message: actionData?.message || 'Warning from admin',
            issuedBy: adminAuth.uid,
            issuedAt: admin.firestore.Timestamp.now(),
            read: false
          };
          updateData.warnings = admin.firestore.FieldValue.arrayUnion(warning);
          
          // Send notification to user
          await admin.firestore().collection('notifications').add({
            userId: userId,
            type: 'warning',
            title: 'Warning from Admin',
            message: warning.message,
            read: false,
            createdAt: admin.firestore.Timestamp.now(),
            issuedBy: adminAuth.uid
          });
          break;
          
        default:
          throw new functions.https.HttpsError('invalid-argument', `Invalid action: ${action}`);
      }

      // Update user document
      await admin.firestore().collection('users').doc(userId).update(updateData);

      // Log admin action
      await admin.firestore().collection('adminLogs').add({
        adminId: adminAuth.uid,
        action: `user_${action}`,
        targetUserId: userId,
        timestamp: admin.firestore.Timestamp.now(),
        metadata: actionData || {}
      });

      return {
        success: true,
        message: `User ${action} completed successfully`,
        userId
      };

    } catch (error) {
      console.error('Error in adminManageUser:', error);
      throw new functions.https.HttpsError(
        'internal',
        `Failed to ${data.action} user`,
        error
      );
    }
  });

// Test function
export const testAdminFunctions = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Admin functions working',
      timestamp: new Date().toISOString()
    });
  });
