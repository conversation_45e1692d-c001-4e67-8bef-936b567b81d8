"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.testAdminFunctions = exports.getAdminTransactionData = exports.adminManageUser = exports.grantWalletCredits = exports.getAdminWalletData = void 0;
// Essential admin functions only
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
if (!admin.apps.length) {
    admin.initializeApp();
}
console.log('🚀 Essential Admin Functions loading...');
// Helper functions
const verifyAuth = async (context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }
    return context.auth;
};
const verifyAdminAuth = async (context) => {
    var _a;
    const auth = await verifyAuth(context);
    // Check admin role in Firestore
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }
    return auth;
};
// Get admin wallet data for any user
exports.getAdminWalletData = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        await verifyAdminAuth(context);
        const { userId } = data;
        if (!userId) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
        }
        // Get wallet data
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists) {
            // Create default wallet if it doesn't exist
            const referralCode = `user${userId.substring(0, 6)}`;
            const defaultWallet = {
                userId,
                balance: 0,
                referralCode,
                usedReferral: false,
                history: [],
                grantedBy: 'system',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            };
            await admin.firestore().collection('wallets').doc(userId).set(defaultWallet);
            return defaultWallet;
        }
        return walletDoc.data();
    }
    catch (error) {
        console.error('Error in getAdminWalletData:', error);
        throw error;
    }
});
// Grant wallet credits to users
exports.grantWalletCredits = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        const adminAuth = await verifyAdminAuth(context);
        const { userId, amount, description } = data;
        if (!userId || !amount || amount <= 0) {
            throw new functions.https.HttpsError('invalid-argument', 'Valid user ID and positive amount are required');
        }
        // Create transaction record
        const transaction = {
            id: admin.firestore().collection('temp').doc().id,
            userId: userId,
            type: 'admin_grant',
            amount: parseFloat(amount.toFixed(2)),
            description: description || `Admin credit grant`,
            timestamp: admin.firestore.Timestamp.now(),
            grantedBy: adminAuth.uid,
            source: 'admin_grant',
            createdAt: admin.firestore.Timestamp.now()
        };
        // Get or create wallet
        const walletRef = admin.firestore().collection('wallets').doc(userId);
        const walletDoc = await walletRef.get();
        if (!walletDoc.exists) {
            // Create new wallet
            const referralCode = `user${userId.substring(0, 6)}`;
            await walletRef.set({
                userId,
                balance: transaction.amount,
                referralCode,
                usedReferral: false,
                history: [transaction],
                grantedBy: 'admin',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        else {
            // Update existing wallet
            await walletRef.update({
                balance: admin.firestore.FieldValue.increment(transaction.amount),
                history: admin.firestore.FieldValue.arrayUnion(transaction),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        // Log admin action
        await admin.firestore().collection('adminLogs').add({
            adminId: adminAuth.uid,
            action: 'grant_wallet_credits',
            targetUserId: userId,
            amount: transaction.amount,
            timestamp: admin.firestore.Timestamp.now(),
            metadata: { description }
        });
        return {
            success: true,
            message: `Successfully granted $${transaction.amount} to user`,
            transaction
        };
    }
    catch (error) {
        console.error('Error in grantWalletCredits:', error);
        throw error;
    }
});
// Admin user management function
exports.adminManageUser = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    try {
        const adminAuth = await verifyAdminAuth(context);
        const { userId, action, data: actionData } = data;
        if (!userId || !action) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID and action are required');
        }
        let updateData = {
            updatedAt: admin.firestore.Timestamp.now()
        };
        switch (action) {
            case 'ban':
                updateData.status = 'banned';
                updateData.bannedAt = admin.firestore.Timestamp.now();
                updateData.bannedBy = adminAuth.uid;
                updateData.banReason = (actionData === null || actionData === void 0 ? void 0 : actionData.reason) || 'Banned by admin';
                // Disable Firebase Auth account
                await admin.auth().updateUser(userId, { disabled: true });
                break;
            case 'unban':
                updateData.status = 'active';
                updateData.bannedAt = admin.firestore.FieldValue.delete();
                updateData.bannedBy = admin.firestore.FieldValue.delete();
                updateData.banReason = admin.firestore.FieldValue.delete();
                updateData.unbannedAt = admin.firestore.Timestamp.now();
                updateData.unbannedBy = adminAuth.uid;
                // Re-enable Firebase Auth account
                await admin.auth().updateUser(userId, { disabled: false });
                break;
            case 'block':
                updateData.status = 'blocked';
                updateData.blockedAt = admin.firestore.Timestamp.now();
                updateData.blockedBy = adminAuth.uid;
                updateData.blockReason = (actionData === null || actionData === void 0 ? void 0 : actionData.reason) || 'Blocked by admin';
                break;
            case 'unblock':
                updateData.status = 'active';
                updateData.blockedAt = admin.firestore.FieldValue.delete();
                updateData.blockedBy = admin.firestore.FieldValue.delete();
                updateData.blockReason = admin.firestore.FieldValue.delete();
                updateData.unblockedAt = admin.firestore.Timestamp.now();
                updateData.unblockedBy = adminAuth.uid;
                break;
            case 'warn':
                // Add warning to user's warnings array
                const warning = {
                    id: admin.firestore().collection('temp').doc().id,
                    message: (actionData === null || actionData === void 0 ? void 0 : actionData.message) || 'Warning from admin',
                    issuedBy: adminAuth.uid,
                    issuedAt: admin.firestore.Timestamp.now(),
                    read: false
                };
                updateData.warnings = admin.firestore.FieldValue.arrayUnion(warning);
                // Send notification to user
                await admin.firestore().collection('notifications').add({
                    userId: userId,
                    type: 'warning',
                    title: 'Warning from Admin',
                    message: warning.message,
                    read: false,
                    createdAt: admin.firestore.Timestamp.now(),
                    issuedBy: adminAuth.uid
                });
                break;
            default:
                throw new functions.https.HttpsError('invalid-argument', `Invalid action: ${action}`);
        }
        // Update user document
        await admin.firestore().collection('users').doc(userId).update(updateData);
        // Log admin action
        await admin.firestore().collection('adminLogs').add({
            adminId: adminAuth.uid,
            action: `user_${action}`,
            targetUserId: userId,
            timestamp: admin.firestore.Timestamp.now(),
            metadata: actionData || {}
        });
        return {
            success: true,
            message: `User ${action} completed successfully`,
            userId
        };
    }
    catch (error) {
        console.error('Error in adminManageUser:', error);
        throw new functions.https.HttpsError('internal', `Failed to ${data.action} user`, error);
    }
});
// Get detailed transaction data for admin
exports.getAdminTransactionData = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        await verifyAdminAuth(context);
        const { transactionId } = data;
        if (!transactionId) {
            throw new functions.https.HttpsError('invalid-argument', 'Transaction ID is required');
        }
        // Get transaction data
        const orderDoc = await admin.firestore().collection('orders').doc(transactionId).get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Transaction not found');
        }
        const orderData = orderDoc.data();
        // Get listing data if available
        let listingData = null;
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingDoc = await admin.firestore().collection('listings').doc(orderData.listingId).get();
            if (listingDoc.exists) {
                listingData = listingDoc.data();
            }
        }
        // Get buyer and seller data
        let buyerData = null;
        let sellerData = null;
        if (orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) {
            const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
            if (buyerDoc.exists) {
                const buyer = buyerDoc.data();
                buyerData = {
                    uid: buyer === null || buyer === void 0 ? void 0 : buyer.uid,
                    name: buyer === null || buyer === void 0 ? void 0 : buyer.name,
                    email: buyer === null || buyer === void 0 ? void 0 : buyer.email,
                    university: buyer === null || buyer === void 0 ? void 0 : buyer.university
                };
            }
        }
        if (orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) {
            const sellerDoc = await admin.firestore().collection('users').doc(orderData.sellerId).get();
            if (sellerDoc.exists) {
                const seller = sellerDoc.data();
                sellerData = {
                    uid: seller === null || seller === void 0 ? void 0 : seller.uid,
                    name: seller === null || seller === void 0 ? void 0 : seller.name,
                    email: seller === null || seller === void 0 ? void 0 : seller.email,
                    university: seller === null || seller === void 0 ? void 0 : seller.university
                };
            }
        }
        return {
            transaction: Object.assign(Object.assign({ id: transactionId }, orderData), { 
                // Ensure amounts are properly displayed
                totalAmount: (orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount) || (orderData === null || orderData === void 0 ? void 0 : orderData.amount) || 0, itemPrice: (orderData === null || orderData === void 0 ? void 0 : orderData.itemPrice) || 0, shippingCost: (orderData === null || orderData === void 0 ? void 0 : orderData.shippingCost) || 0, walletAmountUsed: (orderData === null || orderData === void 0 ? void 0 : orderData.walletBalanceUsed) || (orderData === null || orderData === void 0 ? void 0 : orderData.walletAmountUsed) || 0, originalTotal: (orderData === null || orderData === void 0 ? void 0 : orderData.originalTotal) || 0 }),
            listing: listingData,
            buyer: buyerData,
            seller: sellerData
        };
    }
    catch (error) {
        console.error('Error in getAdminTransactionData:', error);
        throw error;
    }
});
// Test function
exports.testAdminFunctions = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Admin functions working',
        timestamp: new Date().toISOString()
    });
});
